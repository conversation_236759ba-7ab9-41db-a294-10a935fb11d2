import { defineStore } from 'pinia';
import type { ChatSession, ChatSessionMetadata, MessageTurnResponse } from '@/types/chat'; // 确保 MessageTurnResponse 被导入如果需要
import { chatApiService } from '@/services/apiService';

interface ChatState {
  currentChatId: string | null;
  chatList: ChatSessionMetadata[];
  isLoading: boolean;
  error: string | null;
}

export interface ChatGroup {
  title: string;
  items: ChatSessionMetadata[];
}

const store = defineStore('chat', {
  state: () => ({
    currentChatId: null,
    chatList: [],
    isLoading: false,
    error: null
  } as ChatState),

  getters: {
    getCurrentChatId: (state) => state.currentChatId,
    getChatList: (state) => state.chatList,
    isCurrentlyLoading: (state) => state.isLoading,
    getError: (state) => state.error,

    getGroupedChatList: (state): ChatGroup[] => {
      const now = new Date();
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const recent: ChatSessionMetadata[] = [];
      const older: ChatSessionMetadata[] = [];

      state.chatList.forEach((chat: ChatSessionMetadata) => {
        const chatDate = new Date(chat.updated_at); // 使用 updated_at 进行分组
        if (chatDate >= sevenDaysAgo) {
          recent.push(chat);
        } else {
          older.push(chat);
        }
      });

      const groups: ChatGroup[] = [];
      if (recent.length > 0) {
        groups.push({ title: '7 天内', items: recent.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()) }); // 按更新时间降序
      }
      if (older.length > 0) {
        groups.push({ title: '更早', items: older.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()) }); // 按更新时间降序
      }

      return groups;
    }
  },

  actions: {
    setCurrentChatId(chatId: string | null) {
      this.currentChatId = chatId;
    },

    setChatList(chatList: ChatSessionMetadata[]) {
      // 确保列表按 updated_at 降序排列，以符合通常的显示顺序
      this.chatList = chatList.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
    },

    addChat(chat: ChatSessionMetadata) {
      // 添加到列表顶部，并重新排序以防万一
      this.chatList.unshift(chat);
      this.chatList.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
    },

    removeChat(chatId: string) {
      this.chatList = this.chatList.filter((chat: ChatSessionMetadata) => chat.id !== chatId);
    },

    updateChatMetadata(chatId: string, newTitle: string, updatedAt: string) {
      const chat = this.chatList.find((chat: ChatSessionMetadata) => chat.id === chatId);
      if (chat) {
        chat.title = newTitle;
        chat.updated_at = updatedAt; // 使用后端返回的精确时间
        // 重新排序列表以确保更新的对话在正确的位置
        this.chatList.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
      }
    },

    setLoading(loading: boolean) {
      this.isLoading = loading;
    },

    setError(error: string | null) {
      this.error = error;
    },

    clearCurrentChat() {
      this.currentChatId = null;
    },

    async fetchChatList() {
      this.setLoading(true);
      this.setError(null);
      try {
        const chatList = await chatApiService.getChats(); // getChats 返回 ChatSessionMetadata[]
        this.setChatList(chatList);
      } catch (error) {
        console.error('Failed to fetch chat list:', error);
        this.setError('获取对话列表失败');
      } finally {
        this.setLoading(false);
      }
    },

    async createNewChat(title: string = '新对话'): Promise<ChatSessionMetadata | null> { // 返回 ChatSessionMetadata
      this.setLoading(true);
      this.setError(null);
      try {
        // chatApiService.createChat 现在应该返回 ChatSessionMetadata (或者包含此信息的 ChatSession)
        // 假设 createChat 返回的是包含 id, title, created_at, updated_at 的对象
        const newChatMeta = await chatApiService.createChat(title); // 后端接口 /chats POST 返回的是 to_dict_metadata()

        if (newChatMeta) {
             this.addChat(newChatMeta);
             this.setCurrentChatId(newChatMeta.id);
             return newChatMeta;
        }
        return null;

      } catch (error) {
        console.error('Failed to create new chat:', error);
        this.setError('创建新对话失败');
        return null;
      } finally {
        this.setLoading(false);
      }
    },

    async deleteChatById(chatId: string): Promise<boolean> {
      this.setLoading(true);
      this.setError(null);
      try {
        await chatApiService.deleteChat(chatId);
        this.removeChat(chatId);
        if (this.currentChatId === chatId) {
          this.clearCurrentChat();
        }
        return true;
      } catch (error) {
        console.error('Failed to delete chat:', error);
        this.setError('删除对话失败');
        return false;
      } finally {
        this.setLoading(false);
      }
    },

    async renameChatById(chatId: string, newTitle: string): Promise<boolean> {
      if (!newTitle.trim() || newTitle.length > 150) {
        this.setError('标题长度必须在1-150字符之间');
        return false;
      }

      this.setLoading(true);
      this.setError(null);
      try {
        // 假设 renameChat API 调用成功后，后端会返回更新后的时间和标题，或者我们从响应中获取
        const response = await chatApiService.renameChat(chatId, newTitle.trim()); // API 可能返回 { message, id, new_title, updated_at }
        
        // 从后端响应中获取精确的 updated_at (如果API返回的话)
        // 否则, 乐观更新或重新拉取列表/该项
        // 假设 response 包含类似 { id: string, new_title: string, updated_at: string } 的结构或从 chat_session_meta_update 提取
        // 暂时使用本地时间，但最好用后端时间
        const chatToUpdate = this.chatList.find(c => c.id === chatId);
        if (chatToUpdate) {
            // 如果API返回了updated_at，使用它
            // 否则，可以使用 new Date().toISOString() 但这可能不准确
            // 为了演示，我们假设 chatApiService.renameChat 返回了更新后的 ChatSessionMetadata 或者足够的信息
            // 例如: response = { id: "...", title: "...", updated_at: "..." }
            // 或后端返回类似 { message: '...', id: '...', new_title: '...', updated_at: 'ISO_STRING' }
            // 在这里，我们直接更新，并依赖 chat_session_meta_update (如果 MessageTurnResponse 类似结构返回)
            // 实际上 renameChat 的响应更可能是简单的成功消息和更新后的标题/时间戳
            // 我们先简单更新标题和本地时间，实际应用中应以服务器返回为准
             this.updateChatMetadata(chatId, newTitle.trim(), new Date().toISOString()); // TODO: Use actual updated_at from API response
        }

        return true;
      } catch (error) {
        console.error('Failed to rename chat:', error);
        this.setError('重命名对话失败');
        return false;
      } finally {
        this.setLoading(false);
      }
    },

    // 用于处理消息发送后，更新对话列表中的元数据
    updateChatListMeta(chatId: string, updatedAt: string) {
        const chat = this.chatList.find(c => c.id === chatId);
        if (chat) {
            chat.updated_at = updatedAt;
            this.chatList.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
        }
    }
  }
});

export type ChatStoreType = ReturnType<typeof store>;
export const useChatStore = store;