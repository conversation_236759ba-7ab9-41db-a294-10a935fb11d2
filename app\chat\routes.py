from flask import request, jsonify, current_app
from flask_login import login_required, current_user
from app import db # 确保 db 实例已正确初始化
from app.models import Conversation, ChatMessageNode, SystemSetting # 导入模型
from app.chat import bp # 假设 bp = Blueprint('chat', __name__, url_prefix='/api')
from app.services.llm_service import get_llm_completion # LLM 服务接口
# import uuid # uuid 已在 models.py 中用于 node_id 默认值
from datetime import datetime

# 辅助函数：为 LLM 构建历史记录 (简化版)
def build_llm_history(chat_id: int):
    """
    从数据库中获取指定对话的所有消息，按时间顺序排列，构建 LLM 需要的 {role, content} 列表。
    """
    history_for_llm = []

    # 获取系统提示
    system_prompt_setting = SystemSetting.query.filter_by(key='system_prompt').first()
    system_prompt_content = system_prompt_setting.value if system_prompt_setting else "You are a helpful assistant."
    history_for_llm.append({"role": "system", "content": system_prompt_content})

    # 获取该对话的所有消息，按时间戳升序排列
    # ChatMessageNode 在 Conversation.message_nodes relationship 中已定义 order_by
    # 或者直接查询并排序
    messages = ChatMessageNode.query.filter_by(conversation_id=chat_id)\
                                    .order_by(ChatMessageNode.timestamp.asc()).all()

    for msg_node in messages:
        history_for_llm.append({'role': msg_node.role, 'content': msg_node.content})
    
    return history_for_llm


@bp.route('/chats', methods=['POST'])
@login_required
def create_chat_session():
    data = request.get_json()
    title = data.get('title', 'New Chat')
    # entry_node_id 不再需要
    conversation = Conversation(user_id=current_user.id, title=title)
    db.session.add(conversation)
    db.session.commit()
    response_data = conversation.to_dict_metadata() # 使用 to_dict_metadata 更合适，因为此时还没有消息
    return jsonify(response_data), 201

@bp.route('/chats', methods=['GET'])
@login_required
def get_chat_sessions_list():
    conversations = Conversation.query.filter_by(user_id=current_user.id).order_by(Conversation.updated_at.desc()).all()
    return jsonify([c.to_dict_metadata() for c in conversations]), 200

@bp.route('/chats/<string:chat_id_str>', methods=['GET'])
@login_required
def get_chat_session_details(chat_id_str):
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()
    # to_dict_full() 将返回包含排序后消息的完整对话数据
    return jsonify(conversation.to_dict_full()), 200

@bp.route('/chats/<string:chat_id_str>', methods=['DELETE'])
@login_required
def delete_chat_session(chat_id_str):
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()
    db.session.delete(conversation) #级联删除将处理 ChatMessageNodes
    db.session.commit()
    return jsonify({'message': 'Conversation deleted'}), 200

@bp.route('/chats/<string:chat_id_str>/rename', methods=['PUT'])
@login_required
def rename_chat_session(chat_id_str):
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()
    data = request.get_json()
    new_title = data.get('title')
    if not new_title or len(new_title.strip()) == 0 or len(new_title) > 150 :
        return jsonify({'message': 'Valid title is required (1-150 chars)'}), 400

    conversation.title = new_title
    conversation.updated_at = datetime.utcnow()
    db.session.commit()
    return jsonify({'message': 'Conversation title updated', 'id': chat_id_str, 'new_title': new_title}), 200

@bp.route('/chats/<string:chat_id_str>/message_turns', methods=['POST'])
@login_required
def post_message_turn(chat_id_str):
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()
    data = request.get_json()

    user_content = data.get('user_content')
    if not user_content:
        return jsonify({'message': "user_content is required"}), 400

    # 1. 创建用户消息节点
    current_user_node = ChatMessageNode(
        conversation_id=chat_id,
        role='user',
        content=user_content,
        message_metadata=data.get('metadata', {}),
        timestamp=datetime.utcnow()
    )
    db.session.add(current_user_node)
    db.session.flush() # 确保 user_node 对后续的 build_llm_history 可见，并分配了 node_id 和 timestamp

    new_user_node_data = current_user_node.to_dict()

    # 2. 构建历史并调用 LLM 服务
    llm_history = build_llm_history(chat_id) # 此函数现在会获取包括刚添加的 current_user_node 在内的所有历史

    try:
        assistant_content = get_llm_completion(llm_history)
        if assistant_content is None or (isinstance(assistant_content, str) and "Error:" in assistant_content): # 粗略错误检查
             raise Exception(f"LLM service error: {assistant_content}")
    except Exception as e:
        db.session.rollback() # 如果 LLM 调用失败，回滚用户消息的添加
        current_app.logger.error(f"Error getting response from LLM: {e}")
        return jsonify({'message': 'Error getting response from LLM', 'details': str(e)}), 500


    # 3. 创建助手消息节点
    current_assistant_node = ChatMessageNode(
        conversation_id=chat_id,
        role='assistant',
        content=assistant_content,
        message_metadata={}, # 可以添加 LLM 模型信息等
        timestamp=datetime.utcnow()
    )
    db.session.add(current_assistant_node)
    new_assistant_node_data = current_assistant_node.to_dict() # 在 commit 前 to_dict 以获取默认值

    # 更新对话时间戳
    conversation.updated_at = datetime.utcnow()
    db.session.add(conversation)
    
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Database commit error after LLM response: {e}")
        return jsonify({'message': 'Error saving messages to database', 'details': str(e)}), 500


    return jsonify({
        'new_user_node': new_user_node_data,
        'new_assistant_node': new_assistant_node_data,
        'chat_session_meta_update': {
            'id': chat_id_str,
            'updated_at': conversation.updated_at.isoformat() + 'Z',
        }
    }), 201

# /chats/<string:chat_id_str>/entry_node (set_entry_node) 路由已移除
# /chats/<string:chat_id_str>/nodes/<string:node_id>/set_active_branch (set_active_branch) 路由已移除