<template>
  <div class="chat-root" v-if="chatSession">
    <div class="title-bar">
      <input v-model="editableTitle" @blur="saveTitle" @keyup.enter="saveTitle" :disabled="isRenaming" />
      <button @click="confirmDeleteChat" :disabled="isDeleting">删除对话</button>
    </div>
    <div class="messages-container" ref="messagesContainerRef">
      <div class="messages">
        <template v-for="message in displayedMessages" :key="message.node_id">
          <div class="message-item">
            <UserMessage
              v-if="message.role === 'user'"
              :message="message.content"
              :timestamp="message.timestamp"
              :node-id="message.node_id"
            />
            <LlmMessage
              v-else
              :message="message.content"
              :timestamp="message.timestamp"
              :node-id="message.node_id"
            />
          </div>
        </template>
        <div v-if="isSending" class="loading-indicator">正在发送...</div>
      </div>
    </div>
    <div class="input-area">
      <textarea v-model="newMessageContent" @keyup.enter.exact.prevent="sendMessage" placeholder="输入消息..."></textarea>
      <button @click="sendMessage" :disabled="isSending || !newMessageContent.trim()">发送</button>
    </div>
  </div>
  <div v-else-if="isLoadingChatDetails" class="loading-chat-details">正在加载对话...</div>
  <div v-else class="no-chat-selected">请选择或创建一个新的对话。</div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import LlmMessage from './llm-message.vue'; // 假设存在
import UserMessage from './user-message.vue'; // 假设存在
import { chatApiService } from '@/services/apiService';
import { useChatStore } from '@/store/chat';
import type { ChatSession, ChatMessageNode, MessageTurnPayload, MessageTurnResponse } from '@/types/chat';

const props = defineProps<{
  activeChatId: string | null;
}>();

const chatStore = useChatStore();

const chatSession = ref<ChatSession | null>(null);
const isLoadingChatDetails = ref(false); // 用于加载对话详情
const isSending = ref(false); // 用于发送消息
const isRenaming = ref(false);
const isDeleting = ref(false);

const newMessageContent = ref('');
const editableTitle = ref('');
const messagesContainerRef = ref<HTMLElement | null>(null);


// displayedMessages 现在直接是 chatSession 中的消息列表
// 后端应确保 message_nodes 按时间戳升序排列
const displayedMessages = computed<ChatMessageNode[]>(() => {
  return chatSession.value?.message_nodes || [];
});

watch(() => props.activeChatId, async (newId) => {
  if (newId) {
    await loadChatDetails(newId);
  } else {
    chatSession.value = null;
    editableTitle.value = '';
  }
}, { immediate: true });

watch(chatSession, (newChat) => {
  if (newChat) {
    editableTitle.value = newChat.title || '';
    // 滚动到底部
    scrollToBottom();
  }
}, { deep: true });

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainerRef.value) {
      messagesContainerRef.value.scrollTop = messagesContainerRef.value.scrollHeight;
    }
  });
};

async function loadChatDetails(chatId: string) {
  isLoadingChatDetails.value = true;
  chatSession.value = null; // 清空旧对话数据
  try {
    const chatData = await chatApiService.getChatDetails(chatId);
    // 确保 message_nodes 是按时间戳排序的，如果后端没保证，前端排序
    chatData.message_nodes.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    chatSession.value = chatData;
    scrollToBottom();
  } catch (error) {
    console.error("Failed to load chat details:", error);
    chatStore.setError('加载对话详情失败');
    // 可以在UI上显示错误
  } finally {
    isLoadingChatDetails.value = false;
  }
}

async function saveTitle() {
  if (!chatSession.value || !editableTitle.value.trim() || chatSession.value.title === editableTitle.value.trim()) {
    if (chatSession.value) editableTitle.value = chatSession.value.title || ''; // 恢复原标题
    return;
  }
  isRenaming.value = true;
  try {
    // 后端 renameChat 应该返回更新后的 title 和 updated_at
    const response = await chatApiService.renameChat(chatSession.value.id, editableTitle.value.trim());
    if (chatSession.value) {
      chatSession.value.title = response.new_title; // 使用后端返回的新标题
      chatSession.value.updated_at = response.updated_at; // 使用后端返回的更新时间
      chatStore.updateChatMetadata(chatSession.value.id, response.new_title, response.updated_at); // 更新左侧列表
    }
  } catch (error) {
    console.error("Failed to rename chat:", error);
    if (chatSession.value) editableTitle.value = chatSession.value.title || ''; // 发生错误时恢复原标题
    chatStore.setError('重命名对话失败');
  } finally {
    isRenaming.value = false;
  }
}

async function confirmDeleteChat() {
    if(!chatSession.value) return;
    if(confirm(`确定要删除对话 "${chatSession.value.title || '该对话'}" 吗？此操作无法撤销。`)){
        isDeleting.value = true;
        try {
            await chatStore.deleteChatById(chatSession.value.id); // 使用 store action
            // chatSession.value = null; // store action 会处理 currentChatId, 触发 activeChatId 变化
            // 通常，父组件会监听 currentChatId 的变化并清空或切换视图
            // 此处如果 activeChatId 由父组件管理，则不需要手动设为 null
            alert("对话已删除。");
        } catch (error) { // store action 内部会处理错误并设置 error state
            console.error("Failed to delete chat via store:", error);
            // alert("删除对话失败。"); // store 的 error state 可以用于显示
        } finally {
            isDeleting.value = false;
        }
    }
}

// 更新本地 chatSession 状态
function updateChatStateFromResponse(response: MessageTurnResponse) {
  if (!chatSession.value) return;

  const { new_user_node, new_assistant_node, chat_session_meta_update } = response;

  // 1. 添加新节点 (确保按时间排序，如果直接push)
  // 由于我们依赖时间戳排序，直接添加然后依赖 computed property 或下次加载时排序
  chatSession.value.message_nodes.push(new_user_node);
  chatSession.value.message_nodes.push(new_assistant_node);
  // 如果需要严格保持排序，可以在添加后立即排序，但通常显示时排序即可
  // chatSession.value.message_nodes.sort((a,b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());


  // 2. 更新 chat session 元数据
  chatSession.value.updated_at = chat_session_meta_update.updated_at;

  // 3. 更新 Pinia store 中的列表项元数据
  chatStore.updateChatListMeta(chat_session_meta_update.id, chat_session_meta_update.updated_at);

  scrollToBottom();
}

async function sendMessage() {
  if (!newMessageContent.value.trim() || !chatSession.value) return;
  isSending.value = true;

  const payload: MessageTurnPayload = {
    user_content: newMessageContent.value.trim(),
    metadata: { client_type: "web_vue_client" } // 示例元数据
  };

  try {
    const response = await chatApiService.postMessageTurn(chatSession.value.id, payload);
    updateChatStateFromResponse(response);
    newMessageContent.value = ''; // 清空输入框
  } catch (error) {
    console.error("Failed to send message:", error);
    chatStore.setError('发送消息失败');
    // 可在UI上显示错误提示
  } finally {
    isSending.value = false;
  }
}

// 以下函数不再需要，因为编辑、重新生成和版本切换功能已移除：
// - promptEditUserMessage
// - handleRegenerateAssistantMessage
// - handleVersionChange
// - getVersionInfo
// - findNodeById (如果只是为了遍历，displayedMessages 本身就是列表)

</script>

<style lang="scss" scoped>
.chat-root {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f9f9f9;
}

.title-bar {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fff;
  input {
    flex-grow: 1;
    font-size: 1.1em;
    font-weight: 500;
    border: 1px solid transparent;
    padding: 6px 8px;
    border-radius: 4px;
    &:focus {
      outline: none;
      border-color: #007bff;
      background-color: #fff;
    }
     &:disabled {
      background-color: transparent;
      color: #333;
    }
  }
  button {
    margin-left: 10px;
    padding: 6px 12px;
    font-size: 0.9em;
    border-radius: 4px;
    cursor: pointer;
    // 简单样式，可替换为图标按钮等
    background-color: #dc3545;
    color: white;
    border: none;
    &:hover {
        background-color: #c82333;
    }
    &:disabled {
      background-color: #f8d7da;
      cursor: not-allowed;
    }
  }
}

.messages-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 10px 0; // 上下留白
}

.messages {
  display: flex;
  flex-direction: column;
  margin: auto; // 水平居中
  width: 100%;
  max-width: 820px; // 内容最大宽度
  padding: 0 10px; // 左右内边距
}

.loading-indicator, .loading-chat-details, .no-chat-selected {
  padding: 20px;
  text-align: center;
  color: #777;
  font-size: 1.1em;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-area {
  display: flex;
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;
  background-color: #ffffff;
  textarea {
    flex-grow: 1;
    min-height: 44px; // 调整最小高度以适应单行或多行
    max-height: 150px; // 限制最大高度
    padding: 10px 12px;
    border: 1px solid #ccc;
    border-radius: 22px; // 圆角输入框
    resize: none; // 禁止用户调整大小
    font-size: 1em;
    line-height: 1.4;
    margin-right: 10px;
    overflow-y: auto; // 内容多时可滚动
    &:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }
  }
  button {
    padding: 0 20px; // 固定padding
    min-height: 44px; // 与textarea对齐
    font-size: 1em;
    border-radius: 22px; // 圆角按钮
    cursor: pointer;
    background-color: #007bff;
    color: white;
    border: none;
    white-space: nowrap; // 防止文字换行
    &:hover {
      background-color: #0056b3;
    }
    &:disabled {
      background-color: #cce5ff;
      cursor: not-allowed;
    }
  }
}

.message-item {
  margin-bottom: 12px;
  // 子组件 UserMessage/LlmMessage 将负责自己的具体样式
}
</style>