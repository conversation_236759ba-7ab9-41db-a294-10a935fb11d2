/**
 * 工具函数集合
 */

/**
 * 验证中国大陆手机号
 * 支持的格式：
 * - 11位数字，以1开头
 * - 常见的手机号段：13x, 14x, 15x, 16x, 17x, 18x, 19x
 * @param phoneNumber 手机号码
 * @returns 是否是有效的手机号
 */
export function validatePhoneNumber(phoneNumber: string): boolean {
  // 中国大陆手机号正则表达式
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phoneNumber);
}

/**
 * 验证邮箱地址
 * 支持的格式：
 * - 用户名部分：字母、数字、下划线、点、连字符
 * - @ 符号
 * - 域名部分：字母、数字、连字符、点
 * - 顶级域名：2-10个字母
 * @param email 邮箱地址
 * @returns 是否是有效的邮箱地址
 */
export function validateEmail(email: string): boolean {
  // 邮箱地址正则表达式
  const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,10}$/;
  return emailRegex.test(email);
}

/**
 * 验证账号（手机号或邮箱）
 * @param account 账号（手机号或邮箱）
 * @returns 是否是有效的账号
 */
export function validateAccount(account: string): boolean {
  return validatePhoneNumber(account) || validateEmail(account);
}